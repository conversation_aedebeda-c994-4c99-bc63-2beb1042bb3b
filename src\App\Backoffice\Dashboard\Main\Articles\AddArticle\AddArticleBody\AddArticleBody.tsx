import React from 'react';
import { useArticle } from '../../Articles';
import { useDispatch } from 'react-redux';
import { AppDispatch } from '../../../../../../../../utilities/redux/store';
import { refreshArticles } from '../../../../../../../../utilities/redux/backoffice/backofficeSlice';
import { createArticle, updateArticle } from '../../../../../../../../utilities/api/actions';
import useToasts from '../../../../../../../../utilities/minitiatures/Toast/hooks/useToasts';
import Input from '../../../../../../../../utilities/minitiatures/Input/Input';
import Button from '../../../../../../../../utilities/minitiatures/Button/Button';
import { Article } from '../../../../../../../../utilities/constants/types';
import { useSelector } from 'react-redux';
import { RootState } from '../../../../../../../../utilities/redux/store';
import { Category } from '../../../../../../../../utilities/constants/types';
import ArticleContent from './ArticleContent';
import ArticleImages from './ArticleImages';

type Edits = {
    title: string,
    author: string,
    category_id: number | null,
}

const DEFAULT_EDITS: Edits = {
    title: '',
    author: '',
    category_id: null,
}

const AddArticleBody = React.memo(() => {
    const { current, setCurrent } = useArticle();
    const dispatch = useDispatch<AppDispatch>();
    const toasts = useToasts();
    const categories = useSelector((state: RootState) => state.backoffice.categories);

    const [state, setState] = React.useState({
        edits: DEFAULT_EDITS,
        loading: false,
        validationMessages: null as { [key: string]: string } | null,
    });

    const handleTitleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        setState(s => ({ ...s, edits: { ...s.edits, title: value } }));
    }, []);

    const handleAuthorChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = e.target;
        setState(s => ({ ...s, edits: { ...s.edits, author: value } }));
    }, []);

    const handleCategoryChange = React.useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
        const { value } = e.target;
        setState(s => ({ ...s, edits: { ...s.edits, category_id: value ? parseInt(value) : null } }));
    }, []);

    const handleSubmit = React.useCallback((e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setState(s => ({ ...s, loading: true }));

        const payload = {
            ...state.edits,
            category_id: state.edits.category_id || undefined,
        };

        const action = current ? updateArticle(current.id, payload) : createArticle(payload);

        action
            .then(() => {
                toasts.push({
                    title: current ? "Article mis à jour" : "Article créé",
                    content: current ? "L'article a été mis à jour avec succès" : "L'article a été créé avec succès",
                    type: "success",
                });
                setCurrent(null);
                dispatch(refreshArticles());
            })
            .catch((error) => {
                if (error.response?.status === 422) {
                    setState(s => ({
                        ...s,
                        validationMessages: error.response.data.errors,
                    }));
                } else {
                    toasts.push({
                        title: "Erreur",
                        content: "Une erreur s'est produite lors de l'enregistrement de l'article",
                        type: "danger",
                    });
                }
            })
            .finally(() => {
                setState(s => ({ ...s, loading: false }));
            });
    }, [current, state.edits, dispatch, setCurrent, toasts.push]);

    React.useEffect(() => {
        if (current) {
            setState(s => ({
                ...s,
                edits: {
                    title: current.title,
                    author: current.author,
                    category_id: current.category_id,
                },
            }));
        } else {
            setState(s => ({
                ...s,
                edits: DEFAULT_EDITS,
                validationMessages: null,
            }));
        }
    }, [current]);

    return <form onSubmit={handleSubmit}>
        <div className="mb-3">
            <label htmlFor="article-title" className="form-label">Titre de l'article *</label>
            <Input
                type="text"
                id="article-title"
                className="form-control"
                placeholder="Titre de l'article"
                value={state.edits.title}
                onChange={handleTitleChange}
                options={{ error: state.validationMessages?.title }}
                required
            />
        </div>

        <div className="mb-3">
            <label htmlFor="article-author" className="form-label">Auteur *</label>
            <Input
                type="text"
                id="article-author"
                className="form-control"
                placeholder="Nom de l'auteur"
                value={state.edits.author}
                onChange={handleAuthorChange}
                options={{ error: state.validationMessages?.author }}
                required
            />
        </div>

        <div className="mb-3">
            <label htmlFor="article-category" className="form-label">Catégorie *</label>
            <select
                id="article-category"
                className="form-select"
                value={state.edits.category_id || ''}
                onChange={handleCategoryChange}
                required>
                <option value="">Sélectionner une catégorie</option>
                {categories?.map(category => (
                    <option key={category.id} value={category.id}>
                        {category.name}
                    </option>
                ))}
            </select>
            {state.validationMessages?.category_id && (
                <div className="invalid-feedback d-block">
                    {state.validationMessages.category_id}
                </div>
            )}
        </div>

        {current && (
            <>
                <div className="mb-3">
                    <label className="form-label">Contenu de l'article</label>
                    <ArticleContent
                        articleId={current.id}
                        sections={current.sections}
                        onSectionsChange={(sections) => {
                            setCurrent({
                                ...current,
                                sections,
                            });
                        }}
                    />
                </div>

                <div className="mb-3">
                    <label className="form-label">Images de l'article</label>
                    <ArticleImages
                        articleId={current.id}
                        images={current.images}
                        onImagesChange={(images) => {
                            setCurrent({
                                ...current,
                                images,
                            });
                        }}
                    />
                </div>
            </>
        )}

        <div className="add-article-actions">
            <Button
                type="button"
                className="btn btn-outline-dark"
                onClick={() => setCurrent(null)}>
                Annuler
            </Button>
            <Button
                type="submit"
                className="btn btn-secondary"
                options={{ loading: state.loading }}>
                {current ? "Mettre à jour" : "Créer"}
            </Button>
        </div>
    </form>
});

export default AddArticleBody; 