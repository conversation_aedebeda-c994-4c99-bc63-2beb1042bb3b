import * as React from "react";
import { Modal } from "react-bootstrap";
import Input from "../../../../../../../utilities/minitiatures/Input/Input";
import SelectedCategory from "../../../Categories/AddCategory/SelectedCategory/SelectedCategory";
import Button from "../../../../../../../utilities/minitiatures/Button/Button";
import useCategorySelect from "../../../../../../../utilities/minitiatures/CategorySelect/hooks/useCategorySelect";
import { Category, Section, Image as ArticleImage } from "../../../../../../../utilities/constants/types";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, Rootstate } from "../../../../../../../utilities/redux/store";
import { refreshArticles } from "../../../../../../../utilities/redux/backoffice/backofficeSlice";
import useToasts from "../../../../../../../utilities/minitiatures/Toast/hooks/useToasts";
import { createArticle } from "../../../../../../../utilities/api/actions";
import { AxiosError } from "axios";
import truthyEntriesOnly from "../../../../../../../utilities/helpers/truthyEntriesOnly";
import ArticleContent from "./ArticleContent";
import ArticleImages from "./ArticleImages";

type Props = {
    setShow: (show: boolean) => void
}

const DEFAULT_INPUT_VALUES = {
    title: '',
    author: '',
    category: null as Category | null,
    sections: [] as Section[],
    images: [] as ArticleImage[],
}

const AddArticleBody = React.memo((props: Props) => {
    const toasts = useToasts();
    const categorySelect = useCategorySelect();
    const dispatch = useDispatch<AppDispatch>();

    const [state, setState] = React.useState({
        inputValues: DEFAULT_INPUT_VALUES,
        loading: false,
        validationMessages: null as null | { [key: string]: any }
    });

    const HandleCategorySelectClose = React.useCallback((selected: Category | null) => {
        setState(s => ({ ...s, inputValues: { ...s.inputValues, category: selected } }))
    }, []);

    const handleOpenCategorySelect = React.useCallback(() => {
        categorySelect.open(HandleCategorySelectClose, state.inputValues.category?.id);
    }, [categorySelect.open, HandleCategorySelectClose, state.inputValues.category]);

    const handleChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setState(s => ({ ...s, inputValues: { ...s.inputValues, [name]: value } }));
    }, []);

    const handleSectionsChange = React.useCallback((sections: Section[]) => {
        setState(s => ({ ...s, inputValues: { ...s.inputValues, sections } }));
    }, []);

    const handleImagesChange = React.useCallback((images: ArticleImage[]) => {
        setState(s => ({ ...s, inputValues: { ...s.inputValues, images } }));
    }, []);

    const handleSubmit = React.useCallback(() => {
        setState(s => ({ ...s, loading: true }));
        const newState = { ...state };

        const payload = truthyEntriesOnly({
            title: state.inputValues.title,
            author: state.inputValues.author,
            category_id: state.inputValues.category?.id,
            sections: state.inputValues.sections.length > 0 ? state.inputValues.sections : undefined,
            images: state.inputValues.images.length > 0 ? state.inputValues.images.map((img: ArticleImage) => ({
                url: img.url, // Le blob/File object
                caption: img.caption,
                order: img.order
            })) : undefined,
        }) || {};

        createArticle(payload)
            .then(() => {
                newState.inputValues = DEFAULT_INPUT_VALUES;

                props.setShow(false);
                toasts.push({
                    title: "Article créé!",
                    content: "L'article a été créé avec succès",
                    type: "success",
                });

                dispatch(refreshArticles());
            })
            .catch((error: AxiosError) => {
                const { errors } = error.response?.data as { errors: null };
                if (errors) {
                    newState.validationMessages = errors;
                } else {
                    toasts.push({
                        title: "Impossible de créer l'article",
                        content: "Une erreur a été rencontrée lors de la création de l'article.",
                        type: "danger",
                    });
                }
            })
            .finally(() => {
                newState.loading = false;
                setState(newState);
            });
    }, [state, props.setShow, toasts.push, dispatch]);

    return <>
        <Modal.Header closeButton>
            <Modal.Title>
                Création d'un article
            </Modal.Title>
        </Modal.Header>
        <Modal.Body className="d-flex flex-wrap justify-content-between add-article-modal-body px-5">
            <div className="col-5 my-3">
                <label htmlFor="article-title">Titre de l'article *</label>
                <Input
                    type="text"
                    placeholder="Le titre de votre article"
                    name="title"
                    id="article-title"
                    onChange={handleChange}
                    value={state.inputValues.title}
                    options={{ error: state.validationMessages?.title }} />
            </div>
            <div className="col-5 my-3">
                <label htmlFor="article-author">Auteur de l'article *</label>
                <Input
                    type="text"
                    placeholder="L'auteur de l'article"
                    name="author"
                    id="article-author"
                    onChange={handleChange}
                    value={state.inputValues.author}
                    options={{ error: state.validationMessages?.author }} />
            </div>
            <div className="col-5 my-3 d-flex justify-content-between">
                <div>
                    <h6>Catégorie de l'article *</h6>
                    <SelectedCategory category={state.inputValues.category} />
                </div>
                <Button
                    type="button"
                    className="btn btn-outline-dark btn-sm align-self-start"
                    onClick={handleOpenCategorySelect}>Ouvrir <i className="fa fa-external-link"></i></Button>
            </div>
            <div className="col-5 my-3">
                <h6>Images de l'article</h6>
                <ArticleImages
                    images={state.inputValues.images}
                    onChange={handleImagesChange}
                />
            </div>
            <div className="col-12 my-3">
                <h6>Contenu de l'article</h6>
                <ArticleContent
                    sections={state.inputValues.sections}
                    onChange={handleSectionsChange}
                />
            </div>
        </Modal.Body>
        <Modal.Footer>
            <Button type="button" className="btn btn-outline-dark btn-sm" onClick={() => props.setShow(false)}>Annuler</Button>
            <Button
                type="button"
                className="btn btn-primary"
                onClick={handleSubmit}
                options={{ loading: state.loading }}>
                <i className="fa fa-check"></i> Enregistrer
            </Button>
        </Modal.Footer>
    </>
});

export default AddArticleBody; 