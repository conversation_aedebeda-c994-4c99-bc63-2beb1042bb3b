import React from 'react';
import { Image } from '../../../../../../../../utilities/constants/types';
import Button from '../../../../../../../../utilities/minitiatures/Button/Button';
import { deleteArticleImage } from '../../../../../../../../utilities/api/actions';
import useToasts from '../../../../../../../../utilities/minitiatures/Toast/hooks/useToasts';
import ImageInputDD, { Image as ImageInputImage } from '../../../../../../../../utilities/minitiatures/ImageInputDD/ImageInputDD';
import ImageForm from '../../../../../../../../utilities/minitiatures/ImageForm/ImageForm';

type Props = {
    articleId: number;
    images: Image[];
    onImagesChange: (images: Image[]) => void;
}

const ArticleImages = React.memo((props: Props) => {
    const { articleId, images, onImagesChange } = props;
    const toasts = useToasts();

    const [state, setState] = React.useState({
        loading: false,
    });

    const handleImageUpload = React.useCallback((files: File[]) => {
        // TODO: Implement image upload
    }, []);

    const handleImageDelete = React.useCallback(async (imageId: number) => {
        setState(s => ({ ...s, loading: true }));

        try {
            await deleteArticleImage(imageId);
            onImagesChange(images.filter(image => image.id !== imageId));

            toasts.push({
                title: "Image supprimée",
                content: "L'image a été supprimée avec succès",
                type: "success",
            });
        } catch (error) {
            toasts.push({
                title: "Erreur",
                content: "Une erreur s'est produite lors de la suppression de l'image",
                type: "danger",
            });
        } finally {
            setState(s => ({ ...s, loading: false }));
        }
    }, [images, onImagesChange, toasts.push]);

    const handleImageChange = React.useCallback((updatedImage: Image) => {
        const newImages = images.map(image => 
            image.id === updatedImage.id ? updatedImage : image
        );
        onImagesChange(newImages);
    }, [images, onImagesChange]);

    return (
        <div className="article-images">
            <div className="images-list">
                {images.map(image => (
                    <ImageForm
                        key={image.id}
                        image={image}
                        onChange={handleImageChange}
                        onRemove={() => handleImageDelete(image.id)}
                    />
                ))}
            </div>

            <div className="add-image">
                <ImageInputDD
                    id="article-image-input"
                    size="md"
                    addImage={({ imageUrl }: ImageInputImage) => {
                        if (imageUrl) {
                            onImagesChange([
                                ...images,
                                {
                                    id: Date.now(),
                                    url: imageUrl,
                                    caption: '',
                                }
                            ]);
                        }
                    }}
                    removeImage={() => {}}
                />
            </div>
        </div>
    );
});

export default ArticleImages; 