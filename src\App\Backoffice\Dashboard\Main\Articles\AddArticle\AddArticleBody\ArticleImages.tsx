import * as React from "react";
import generateArray from "../../../../../../../utilities/helpers/generateArray";
import ImageInputDD, { Image } from "../../../../../../../utilities/minitiatures/ImageInputDD/ImageInputDD";
import { Image as ArticleImage } from "../../../../../../../utilities/constants/types";

type Props = {
    images: ArticleImage[],
    onChange: (images: ArticleImage[]) => void,
    count?: number,
}

const ArticleImages = React.memo((props: Props) => {
    const { count = 4, images, onChange } = props;
    const length = React.useMemo(() => (images.length + 1) < count ? images.length + 1 : count, [count, images]);

    const addImage = React.useCallback((image: Image) => {
        const newImages = [...images];
        if (newImages.length < count) {
            // Pour les articles, on ajoute les métadonnées caption et order
            const articleImage: ArticleImage = {
                id: Date.now(), // Temporary ID for new images
                url: image.imageUrl || '',
                caption: '', // Caption vide par défaut, peut être modifié plus tard
                order: newImages.length + 1
            };
            newImages.push(articleImage);
            onChange(newImages);
        }
    }, [images, count, onChange]);

    const removeImage = React.useCallback((url: string) => {
        const newImages = images.filter(image => image.url !== url);
        // Réorganiser les ordres après suppression
        const reorderedImages = newImages.map((img, index) => ({
            ...img,
            order: index + 1
        }));
        onChange(reorderedImages);
    }, [images, onChange]);

    const updateImageCaption = React.useCallback((url: string, caption: string) => {
        const newImages = images.map(image =>
            image.url === url ? { ...image, caption } : image
        );
        onChange(newImages);
    }, [images, onChange]);

    return (
        <div className="article-images-container">
            <div className="d-flex gap-1 mb-3">
                {generateArray(length).map((_, key) => {
                    return <ImageInputDD
                        key={key}
                        imageUrl={images[key]?.url}
                        addImage={addImage}
                        removeImage={removeImage}
                        id={'article-image-input-dd' + key} />
                })}
            </div>

            {/* Section pour les captions des images */}
            {images.length > 0 && (
                <div className="images-captions">
                    <h6 className="mb-2">Légendes des images</h6>
                    {images.map((image, index) => (
                        <div key={index} className="mb-2">
                            <label className="form-label small">
                                Image {index + 1}
                            </label>
                            <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Légende de l'image (optionnel)"
                                value={image.caption || ''}
                                onChange={(e) => updateImageCaption(image.url, e.target.value)}
                            />
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
});

export default ArticleImages;