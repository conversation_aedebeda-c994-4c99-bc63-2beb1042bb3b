import React from 'react';
import { Section } from '../../../../../../../../utilities/constants/types';
import SectionForm from '../../../../../../../../utilities/minitiatures/SectionForm/SectionForm';
import Button from '../../../../../../../../utilities/minitiatures/Button/Button';
import { createArticleSection } from '../../../../../../../../utilities/api/actions';
import useToasts from '../../../../../../../../utilities/minitiatures/Toast/hooks/useToasts';

type Props = {
    articleId: number;
    sections: Section[];
    onSectionsChange: (sections: Section[]) => void;
}

const ArticleContent = React.memo((props: Props) => {
    const { articleId, sections, onSectionsChange } = props;
    const toasts = useToasts();

    const [state, setState] = React.useState({
        loading: false,
    });

    const handleAddSection = React.useCallback(async () => {
        setState(s => ({ ...s, loading: true }));

        try {
            const response = await createArticleSection({
                title: 'Nouvelle section',
                article_id: articleId,
                order: sections.length + 1,
            });

            const newSection = response.data.section;
            onSectionsChange([...sections, newSection]);

            toasts.push({
                title: "Section ajoutée",
                content: "La section a été ajoutée avec succès",
                type: "success",
            });
        } catch (error) {
            toasts.push({
                title: "Erreur",
                content: "Une erreur s'est produite lors de l'ajout de la section",
                type: "danger",
            });
        } finally {
            setState(s => ({ ...s, loading: false }));
        }
    }, [articleId, sections, onSectionsChange, toasts.push]);

    const handleSectionChange = React.useCallback((updatedSection: Section) => {
        const newSections = sections.map(section =>
            section.id === updatedSection.id ? updatedSection : section
        );
        onSectionsChange(newSections);
    }, [sections, onSectionsChange]);

    const handleSectionRemove = React.useCallback((sectionId: number) => {
        onSectionsChange(sections.filter(section => section.id !== sectionId));
    }, [sections, onSectionsChange]);

    return (
        <div className="article-content">
            <div className="sections-list">
                {sections.map(section => (
                    <SectionForm
                        key={section.id}
                        section={section}
                        onChange={handleSectionChange}
                        onRemove={() => handleSectionRemove(section.id)}
                    />
                ))}
            </div>

            <div className="add-section">
                <Button
                    type="button"
                    className="btn btn-primary btn-sm"
                    onClick={handleAddSection}
                    options={{ loading: state.loading }}>
                    <i className="fa fa-plus"></i> Ajouter une section
                </Button>
            </div>
        </div>
    );
});

export default ArticleContent; 